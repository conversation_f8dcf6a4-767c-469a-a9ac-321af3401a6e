[2025-07-22 16:37:52] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-22 16:37:52] local.INFO: WhatsApp message (local development - not sent via <PERSON><PERSON>lio) {"phone":"+212600000010","message":"Your SADEMY verification code is: 454149. This code will expire in 10 minutes.","note":"This message was logged instead of sent because app is in local environment. Set TWILIO_FORCE_SEND=true to send actual messages in local development."} 
[2025-07-22 16:38:48] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:55:33] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:55:33] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:56:24] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:56:24] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:58:29] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:58:29] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:58:33] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 21:58:33] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:00:11] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:00:11] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:00:21] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:00:21] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:00:29] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:02:27] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:02:28] local.INFO: WhatsApp message (local development - not sent via Twilio) {"phone":"+212645455782","message":"Your SADEMY verification code is: 962594. This code will expire in 10 minutes.","note":"This message was logged instead of sent because app is in local environment. Set TWILIO_FORCE_SEND=true to send actual messages in local development."} 
[2025-07-23 22:02:33] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:02:33] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:05:55] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:05:55] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:06:04] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:06:04] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:06:16] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:06:17] local.INFO: WhatsApp message (local development - not sent via Twilio) {"phone":"+212645455781","message":"Your SADEMY verification code is: 541346. This code will expire in 10 minutes.","note":"This message was logged instead of sent because app is in local environment. Set TWILIO_FORCE_SEND=true to send actual messages in local development."} 
[2025-07-23 22:30:59] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:32:18] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:32:18] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/Authenticate.php(15): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#34 {main}
"} 
[2025-07-23 22:32:26] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:32:40] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:35:00] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:35:08] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:35:59] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:36:34] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:40:21] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:40:21] local.ERROR: Target class [role] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [role] does not exist. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:914)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('role')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#41 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"role\" does not exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:912)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(912): ReflectionClass->__construct('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('role')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#42 {main}
"} 
[2025-07-23 22:40:21] local.ERROR: Target class [role] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [role] does not exist. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:914)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('role')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"role\" does not exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:912)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(912): ReflectionClass->__construct('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('role')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#9 {main}
"} 
[2025-07-23 22:40:52] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:40:52] local.ERROR: Target class [role] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [role] does not exist. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:914)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('role')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#41 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"role\" does not exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:912)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(912): ReflectionClass->__construct('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('role')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#42 {main}
"} 
[2025-07-23 22:40:52] local.ERROR: Target class [role] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [role] does not exist. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:914)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('role')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"role\" does not exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:912)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(912): ReflectionClass->__construct('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('role')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#9 {main}
"} 
[2025-07-23 22:59:07] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 22:59:07] local.ERROR: Target class [role] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [role] does not exist. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:914)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('role')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#41 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"role\" does not exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:912)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(912): ReflectionClass->__construct('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('role')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#42 {main}
"} 
[2025-07-23 22:59:07] local.ERROR: Target class [role] does not exist. {"userId":1,"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [role] does not exist. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:914)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('role')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"role\" does not exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:912)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(912): ReflectionClass->__construct('role')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('role')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('role', Array, true)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('role', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('role', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('role')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#9 {main}
"} 
[2025-07-23 23:16:32] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:16:32] local.ERROR: Target class [Spatie\Permission\Middlewares\RoleMiddleware] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [Spatie\\Permission\\Middlewares\\RoleMiddleware] does not exist. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:914)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('Spatie\\\\Permissi...')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('Spatie\\\\Permissi...', Array, true)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('Spatie\\\\Permissi...', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('Spatie\\\\Permissi...', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('Spatie\\\\Permissi...')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#8 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"Spatie\\Permission\\Middlewares\\RoleMiddleware\" does not exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php:912)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(912): ReflectionClass->__construct('Spatie\\\\Permissi...')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('Spatie\\\\Permissi...')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('Spatie\\\\Permissi...', Array, true)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('Spatie\\\\Permissi...', Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('Spatie\\\\Permissi...', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(255): Illuminate\\Foundation\\Application->make('Spatie\\\\Permissi...')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#9 {main}
"} 
[2025-07-23 23:17:17] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:17:25] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:17:52] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:18:35] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:18:55] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:19:56] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:20:01] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:20:59] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:21:08] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:22:18] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:22:18] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/Authenticate.php(15): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#34 {main}
"} 
[2025-07-23 23:22:36] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:23:31] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:40:26] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:40:26] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/Authenticate.php(15): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#34 {main}
"} 
[2025-07-23 23:40:36] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:41:20] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:41:43] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:43:44] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:44:16] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:44:16] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/Authenticate.php(15): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#34 {main}
"} 
[2025-07-23 23:44:26] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:44:26] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: Template created successfully {"template_id":8,"user_id":22,"template_name":"Test Template"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: Template created successfully {"template_id":9,"user_id":34,"template_name":"Test Template"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: Template updated successfully {"template_id":12,"user_id":46,"updated_fields":["name","description","type","updated_by","is_active","file_path","file_original_name","file_size"]} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: Template updated successfully {"template_id":15,"user_id":55,"updated_fields":["updated_by","file_path","file_original_name","file_size","demo_video_path","demo_video_original_name","demo_video_size"]} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:50:40] testing.INFO: Template file downloaded {"template_id":17,"user_id":67,"file_name":"original_name.pdf"} 
[2025-07-23 23:52:19] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:19] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:19] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:19] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:19] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:19] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:19] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: Template created successfully {"template_id":13,"user_id":22,"template_name":"Test Template"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: Template created successfully {"template_id":14,"user_id":34,"template_name":"Test Template"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: Template updated successfully {"template_id":17,"user_id":46,"updated_fields":["name","description","type","updated_by","is_active","file_path","file_original_name","file_size"]} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: Template updated successfully {"template_id":20,"user_id":55,"updated_fields":["updated_by","file_path","file_original_name","file_size","demo_video_path","demo_video_original_name","demo_video_size"]} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: Template deleted successfully {"template_name":"quo et et Template","user_id":58} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: Template file downloaded {"template_id":23,"user_id":67,"file_name":"original_name.pdf"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: Template file downloaded {"template_id":24,"user_id":71,"file_name":"original_name.pdf"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.WARNING: Template file not found on disk {"template_id":26,"file_path":"templates/files/nonexistent.pdf"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/Authenticate.php(15): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(585): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(346): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/api/v1/templat...', Array, Array, Array, Array)
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/tests/Feature/TemplateControllerTest.php(609): Illuminate\\Foundation\\Testing\\TestCase->get('/api/v1/templat...')
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(1548): Tests\\Feature\\TemplateControllerTest->test_download_requires_authentication()
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(686): PHPUnit\\Framework\\TestCase->runTest()
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestRunner.php(106): PHPUnit\\Framework\\TestCase->runBare()
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(516): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\TemplateControllerTest))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestSuite.php(374): PHPUnit\\Framework\\TestCase->run()
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/Application.php(203): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#43 {main}
"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:52:20] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:53:43] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:53:43] testing.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/Authenticate.php(15): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(585): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(346): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/api/v1/templat...', Array, Array, Array, Array)
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/tests/Feature/TemplateControllerTest.php(611): Illuminate\\Foundation\\Testing\\TestCase->get('/api/v1/templat...')
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(1548): Tests\\Feature\\TemplateControllerTest->test_download_requires_authentication()
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(686): PHPUnit\\Framework\\TestCase->runTest()
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestRunner.php(106): PHPUnit\\Framework\\TestCase->runBare()
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(516): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\TemplateControllerTest))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestSuite.php(374): PHPUnit\\Framework\\TestCase->run()
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/Application.php(203): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#43 {main}
"} 
[2025-07-23 23:54:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:54:07] testing.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/Authenticate.php(15): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(585): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(346): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/api/v1/templat...', Array, Array, Array, Array)
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/tests/Feature/TemplateControllerTest.php(611): Illuminate\\Foundation\\Testing\\TestCase->get('/api/v1/templat...')
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(1548): Tests\\Feature\\TemplateControllerTest->test_download_requires_authentication()
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(686): PHPUnit\\Framework\\TestCase->runTest()
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestRunner.php(106): PHPUnit\\Framework\\TestCase->runBare()
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(516): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\TemplateControllerTest))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestSuite.php(374): PHPUnit\\Framework\\TestCase->run()
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/Application.php(203): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#43 {main}
"} 
[2025-07-23 23:55:06] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:06] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:06] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: Template created successfully {"template_id":13,"user_id":22,"template_name":"Test Template"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: Template created successfully {"template_id":14,"user_id":34,"template_name":"Test Template"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: Template updated successfully {"template_id":17,"user_id":46,"updated_fields":["name","description","type","updated_by","is_active","file_path","file_original_name","file_size"]} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: Template updated successfully {"template_id":20,"user_id":55,"updated_fields":["updated_by","file_path","file_original_name","file_size","demo_video_path","demo_video_original_name","demo_video_size"]} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: Template deleted successfully {"template_name":"hic illo sint Template","user_id":58} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: Template file downloaded {"template_id":23,"user_id":67,"file_name":"original_name.pdf"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: Template file downloaded {"template_id":24,"user_id":71,"file_name":"original_name.pdf"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.WARNING: Template file not found on disk {"template_id":27,"file_path":"templates/files/nonexistent.pdf"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(570): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(471): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(585): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(346): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/api/v1/templat...', Array, Array, Array, Array)
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/tests/Feature/TemplateControllerTest.php(611): Illuminate\\Foundation\\Testing\\TestCase->get('/api/v1/templat...')
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(1548): Tests\\Feature\\TemplateControllerTest->test_download_requires_authentication()
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(686): PHPUnit\\Framework\\TestCase->runTest()
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestRunner.php(106): PHPUnit\\Framework\\TestCase->runBare()
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(516): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\TemplateControllerTest))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestSuite.php(374): PHPUnit\\Framework\\TestCase->run()
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/Application.php(203): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#42 {main}
"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:55:07] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:56:02] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:56:02] testing.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(570): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(471): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(585): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(346): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/api/v1/templat...', Array, Array, Array, Array)
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/tests/Feature/TemplateControllerTest.php(611): Illuminate\\Foundation\\Testing\\TestCase->get('/api/v1/templat...')
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(1548): Tests\\Feature\\TemplateControllerTest->test_download_requires_authentication()
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(686): PHPUnit\\Framework\\TestCase->runTest()
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestRunner.php(106): PHPUnit\\Framework\\TestCase->runBare()
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(516): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\TemplateControllerTest))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestSuite.php(374): PHPUnit\\Framework\\TestCase->run()
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/Application.php(203): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#42 {main}
"} 
[2025-07-23 23:56:44] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:56:44] testing.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(570): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(471): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(585): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php(346): Illuminate\\Foundation\\Testing\\TestCase->call('GET', '/api/v1/templat...', Array, Array, Array, Array)
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/tests/Feature/TemplateControllerTest.php(611): Illuminate\\Foundation\\Testing\\TestCase->get('/api/v1/templat...')
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(1548): Tests\\Feature\\TemplateControllerTest->test_download_requires_authentication()
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(686): PHPUnit\\Framework\\TestCase->runTest()
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestRunner.php(106): PHPUnit\\Framework\\TestCase->runBare()
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestCase.php(516): PHPUnit\\Framework\\TestRunner->run(Object(Tests\\Feature\\TemplateControllerTest))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/Framework/TestSuite.php(374): PHPUnit\\Framework\\TestCase->run()
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/src/TextUI/Application.php(203): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/phpunit/phpunit/phpunit(104): PHPUnit\\TextUI\\Application->run(Array)
#42 {main}
"} 
[2025-07-23 23:57:17] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:17] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: Template created successfully {"template_id":13,"user_id":22,"template_name":"Test Template"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:51] testing.INFO: Template created successfully {"template_id":14,"user_id":34,"template_name":"Test Template"} 
[2025-07-23 23:57:51] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: Template updated successfully {"template_id":17,"user_id":46,"updated_fields":["name","description","type","updated_by","is_active","file_path","file_original_name","file_size"]} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: Template updated successfully {"template_id":20,"user_id":55,"updated_fields":["updated_by","file_path","file_original_name","file_size","demo_video_path","demo_video_original_name","demo_video_size"]} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: Template deleted successfully {"template_name":"ipsam praesentium sunt Template","user_id":58} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: Template file downloaded {"template_id":23,"user_id":67,"file_name":"original_name.pdf"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: Template file downloaded {"template_id":24,"user_id":71,"file_name":"original_name.pdf"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.WARNING: Template file not found on disk {"template_id":27,"file_path":"templates/files/nonexistent.pdf"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:57:52] testing.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:59:42] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:59:42] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(570): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(471): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#33 {main}
"} 
[2025-07-23 23:59:51] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-23 23:59:57] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-24 00:00:38] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-24 00:01:02] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-24 00:01:02] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php:477)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(570): route('login')
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Exceptions/Handler.php(471): Illuminate\\Foundation\\Exceptions\\Handler->unauthenticated(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Auth\\AuthenticationException))
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/app/Http/Middleware/ConfigureUploadLimits.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\ConfigureUploadLimits->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#33 {main}
"} 
[2025-07-24 00:01:18] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
[2025-07-24 00:01:18] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"512M","max_execution_time":"600"} 
