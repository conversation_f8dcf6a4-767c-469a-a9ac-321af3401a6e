<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateTemplateRequest;
use App\Http\Requests\UpdateTemplateRequest;
use App\Http\Resources\TemplateResource;
use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class TemplateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Display a listing of templates
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $this->authorize('viewAny', Template::class);

            $validator = Validator::make($request->all(), [
                'type' => 'sometimes|string|in:document,presentation,spreadsheet,design',
                'is_active' => 'sometimes|boolean',
                'search' => 'sometimes|string|max:255',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'sort_by' => 'sometimes|string|in:name,created_at,updated_at,type',
                'sort_order' => 'sometimes|string|in:asc,desc'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Validation failed',
                    $validator->errors()->toArray(),
                    422
                );
            }

            $query = Template::query();

            // Apply filters
            if ($request->filled('type')) {
                $query->where('type', $request->type);
            }

            if ($request->filled('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Search functionality
            if ($request->filled('search')) {
                $query->where(function($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $templates = $query->paginate($request->get('per_page', 15));

            return $this->successResponse(
                'Templates retrieved successfully',
                [
                    'templates' => TemplateResource::collection($templates->items()),
                    'pagination' => [
                        'current_page' => $templates->currentPage(),
                        'last_page' => $templates->lastPage(),
                        'per_page' => $templates->perPage(),
                        'total' => $templates->total(),
                        'from' => $templates->firstItem(),
                        'to' => $templates->lastItem(),
                    ]
                ]
            );

        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            return $this->errorResponse(
                'Unauthorized access',
                ['permission' => ['You do not have permission to view templates.']],
                403
            );
        } catch (\Exception $e) {
            Log::error('Failed to retrieve templates', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Failed to retrieve templates',
                ['general' => ['An unexpected error occurred.']],
                500
            );
        }
    }

    /**
     * Store a newly created template (Admin only)
     */
    public function store(CreateTemplateRequest $request): JsonResponse
    {
        try {
            $this->authorize('create', Template::class);

            DB::beginTransaction();

            $templateData = [
                'name' => $request->name,
                'description' => $request->description,
                'type' => $request->type,
                'is_active' => $request->boolean('is_active', true),
                'created_by' => $request->user()->id,
            ];

            // Handle main file upload
            if ($request->hasFile('file')) {
                $fileResult = $this->handleFileUpload($request->file('file'), 'templates/files');
                if ($fileResult['success']) {
                    $templateData['file_path'] = $fileResult['path'];
                    $templateData['file_original_name'] = $fileResult['original_name'];
                    $templateData['file_size'] = $fileResult['size'];
                } else {
                    DB::rollBack();
                    return $this->errorResponse(
                        'File upload failed',
                        ['file' => [$fileResult['error']]],
                        422
                    );
                }
            }

            // Handle demo video upload
            if ($request->hasFile('demo_video')) {
                $videoResult = $this->handleVideoUpload($request->file('demo_video'), 'templates/videos');
                if ($videoResult['success']) {
                    $templateData['demo_video_path'] = $videoResult['path'];
                    $templateData['demo_video_original_name'] = $videoResult['original_name'];
                    $templateData['demo_video_size'] = $videoResult['size'];
                } else {
                    DB::rollBack();
                    // Clean up already uploaded file if exists
                    if (isset($templateData['file_path'])) {
                        Storage::disk('public')->delete($templateData['file_path']);
                    }
                    return $this->errorResponse(
                        'Demo video upload failed',
                        ['demo_video' => [$videoResult['error']]],
                        422
                    );
                }
            }

            $template = Template::create($templateData);

            DB::commit();

            Log::info('Template created successfully', [
                'template_id' => $template->id,
                'user_id' => $request->user()->id,
                'template_name' => $template->name
            ]);

            return $this->successResponse(
                'Template created successfully',
                new TemplateResource($template),
                201
            );

        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            DB::rollBack();
            return $this->errorResponse(
                'Unauthorized access',
                ['permission' => ['You do not have permission to create templates.']],
                403
            );
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Template creation failed', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'request_data' => $request->only(['name', 'description', 'type', 'is_active']),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Failed to create template',
                ['general' => ['An unexpected error occurred during template creation.']],
                500
            );
        }
    }

    /**
     * Display the specified template
     */
    public function show(Template $template): JsonResponse
    {
        try {
            $this->authorize('view', $template);

            return $this->successResponse(
                'Template retrieved successfully',
                new TemplateResource($template)
            );

        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            return $this->errorResponse(
                'Unauthorized access',
                ['permission' => ['You do not have permission to view this template.']],
                403
            );
        } catch (\Exception $e) {
            Log::error('Failed to retrieve template', [
                'template_id' => $template->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Failed to retrieve template',
                ['general' => ['An unexpected error occurred.']],
                500
            );
        }
    }

    /**
     * Update the specified template (Admin only)
     */
    public function update(UpdateTemplateRequest $request, Template $template): JsonResponse
    {
        try {
            $this->authorize('update', $template);

            DB::beginTransaction();

            $updateData = $request->only(['name', 'description', 'type']);
            $updateData['updated_by'] = $request->user()->id;
            
            if ($request->has('is_active')) {
                $updateData['is_active'] = $request->boolean('is_active');
            }

            $oldFilePath = $template->file_path;
            $oldVideoPath = $template->demo_video_path;

            // Handle file upload
            if ($request->hasFile('file')) {
                $fileResult = $this->handleFileUpload($request->file('file'), 'templates/files');
                if ($fileResult['success']) {
                    $updateData['file_path'] = $fileResult['path'];
                    $updateData['file_original_name'] = $fileResult['original_name'];
                    $updateData['file_size'] = $fileResult['size'];
                } else {
                    DB::rollBack();
                    return $this->errorResponse(
                        'File upload failed',
                        ['file' => [$fileResult['error']]],
                        422
                    );
                }
            }

            // Handle demo video upload
            if ($request->hasFile('demo_video')) {
                $videoResult = $this->handleVideoUpload($request->file('demo_video'), 'templates/videos');
                if ($videoResult['success']) {
                    $updateData['demo_video_path'] = $videoResult['path'];
                    $updateData['demo_video_original_name'] = $videoResult['original_name'];
                    $updateData['demo_video_size'] = $videoResult['size'];
                } else {
                    DB::rollBack();
                    return $this->errorResponse(
                        'Demo video upload failed',
                        ['demo_video' => [$videoResult['error']]],
                        422
                    );
                }
            }

            $template->update($updateData);

            // Delete old files after successful update
            if ($request->hasFile('file') && $oldFilePath) {
                Storage::disk('public')->delete($oldFilePath);
            }
            if ($request->hasFile('demo_video') && $oldVideoPath) {
                Storage::disk('public')->delete($oldVideoPath);
            }

            DB::commit();

            Log::info('Template updated successfully', [
                'template_id' => $template->id,
                'user_id' => $request->user()->id,
                'updated_fields' => array_keys($updateData)
            ]);

            return $this->successResponse(
                'Template updated successfully',
                new TemplateResource($template->fresh())
            );

        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            DB::rollBack();
            return $this->errorResponse(
                'Unauthorized access',
                ['permission' => ['You do not have permission to update this template.']],
                403
            );
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Template update failed', [
                'template_id' => $template->id,
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Failed to update template',
                ['general' => ['An unexpected error occurred during template update.']],
                500
            );
        }
    }

    /**
     * Remove the specified template (Admin only)
     */
    public function destroy(Template $template): JsonResponse
    {
        try {
            $this->authorize('delete', $template);

            DB::beginTransaction();

            $templateName = $template->name;
            $filePath = $template->file_path;
            $videoPath = $template->demo_video_path;

            $template->delete();

            // Delete associated files after successful deletion
            if ($filePath) {
                Storage::disk('public')->delete($filePath);
            }
            if ($videoPath) {
                Storage::disk('public')->delete($videoPath);
            }

            DB::commit();

            Log::info('Template deleted successfully', [
                'template_name' => $templateName,
                'user_id' => request()->user()->id
            ]);

            return $this->successResponse(
                "Template '{$templateName}' deleted successfully"
            );

        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            DB::rollBack();
            return $this->errorResponse(
                'Unauthorized access',
                ['permission' => ['You do not have permission to delete this template.']],
                403
            );
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Template deletion failed', [
                'template_id' => $template->id,
                'error' => $e->getMessage(),
                'user_id' => request()->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Failed to delete template',
                ['general' => ['An unexpected error occurred during template deletion.']],
                500
            );
        }
    }

    /**
     * Download template file (Admin and Collaborateur only)
     */
    public function download(Template $template): BinaryFileResponse|JsonResponse
    {
        try {
            $this->authorize('download', $template);

            if (!$template->file_path) {
                return $this->errorResponse(
                    'No file available',
                    ['file' => ['This template does not have a file attached.']],
                    404
                );
            }

            if (!Storage::disk('public')->exists($template->file_path)) {
                Log::warning('Template file not found on disk', [
                    'template_id' => $template->id,
                    'file_path' => $template->file_path
                ]);

                return $this->errorResponse(
                    'File not found',
                    ['file' => ['The template file is no longer available.']],
                    404
                );
            }

            $filePath = Storage::disk('public')->path($template->file_path);
            $fileName = $template->file_original_name ?? 
                       ($template->name . '.' . pathinfo($template->file_path, PATHINFO_EXTENSION));

            Log::info('Template file downloaded', [
                'template_id' => $template->id,
                'user_id' => request()->user()->id,
                'file_name' => $fileName
            ]);

            return response()->download($filePath, $fileName);

        } catch (\Illuminate\Auth\Access\AuthorizationException $e) {
            return $this->errorResponse(
                'Unauthorized access',
                ['permission' => ['You do not have permission to download this template.']],
                403
            );
        } catch (\Exception $e) {
            Log::error('Template download failed', [
                'template_id' => $template->id,
                'error' => $e->getMessage(),
                'user_id' => request()->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Download failed',
                ['general' => ['An unexpected error occurred during file download.']],
                500
            );
        }
    }

    /**
     * Handle file upload with validation and error handling
     */
    private function handleFileUpload($file, string $directory): array
    {
        try {
            if (!$file->isValid()) {
                return $this->getFileUploadError($file, 'file');
            }

            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs($directory, $filename, 'public');

            if (!$filePath) {
                Log::error('Failed to store file', [
                    'filename' => $filename,
                    'directory' => $directory,
                    'storage_disk' => 'public'
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to store file on disk.'
                ];
            }

            return [
                'success' => true,
                'path' => $filePath,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize()
            ];

        } catch (\Exception $e) {
            Log::error('File upload exception', [
                'error' => $e->getMessage(),
                'directory' => $directory
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred during file upload.'
            ];
        }
    }

    /**
     * Handle video upload with validation and error handling
     */
    private function handleVideoUpload($video, string $directory): array
    {
        try {
            if (!$video->isValid()) {
                return $this->getFileUploadError($video, 'video');
            }

            $filename = Str::uuid() . '.' . $video->getClientOriginalExtension();
            $videoPath = $video->storeAs($directory, $filename, 'public');

            if (!$videoPath) {
                Log::error('Failed to store video', [
                    'filename' => $filename,
                    'directory' => $directory,
                    'storage_disk' => 'public'
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to store video on disk.'
                ];
            }

            return [
                'success' => true,
                'path' => $videoPath,
                'original_name' => $video->getClientOriginalName(),
                'size' => $video->getSize()
            ];

        } catch (\Exception $e) {
            Log::error('Video upload exception', [
                'error' => $e->getMessage(),
                'directory' => $directory
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred during video upload.'
            ];
        }
    }

    /**
     * Get detailed file upload error message
     */
    private function getFileUploadError($file, string $type = 'file'): array
    {
        $errorCode = $file->getError();
        $fileSize = $file->getSize();
        $fileSizeMB = round($fileSize / (1024 * 1024), 2);
        $extension = strtolower($file->getClientOriginalExtension());

        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => "The {$type} exceeds the server upload limit ({$fileSizeMB}MB). Current limit: " . ini_get('upload_max_filesize'),
            UPLOAD_ERR_FORM_SIZE => "The {$type} exceeds the form size limit ({$fileSizeMB}MB).",
            UPLOAD_ERR_PARTIAL => "The {$type} was only partially uploaded. Please try again.",
            UPLOAD_ERR_NO_FILE => "No {$type} was uploaded.",
            UPLOAD_ERR_NO_TMP_DIR => 'Server configuration error: Missing temporary folder.',
            UPLOAD_ERR_CANT_WRITE => "Server configuration error: Failed to write {$type} to disk.",
            UPLOAD_ERR_EXTENSION => "A server extension stopped the {$type} upload.",
        ];

        $errorMessage = $errorMessages[$errorCode] ?? "Unknown {$type} upload error.";

        // Specific error messages for different file types
        if ($errorCode === UPLOAD_ERR_INI_SIZE || $errorCode === UPLOAD_ERR_FORM_SIZE) {
            if ($type === 'video') {
                $errorMessage = "Video file too large ({$fileSizeMB}MB). Maximum allowed: 100MB.";
            } elseif (in_array($extension, ['ppt', 'pptx'])) {
                $errorMessage = "PowerPoint file too large ({$fileSizeMB}MB). Maximum allowed: 15MB.";
            } elseif (in_array($extension, ['doc', 'docx', 'pdf'])) {
                $errorMessage = "Document file too large ({$fileSizeMB}MB). Maximum allowed: 50MB.";
            }
        }

        Log::error('File upload error', [
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'file_name' => $file->getClientOriginalName(),
            'file_size_mb' => $fileSizeMB,
            'file_extension' => $extension,
            'type' => $type
        ]);

        return [
            'success' => false,
            'error' => $errorMessage
        ];
    }

    /**
     * Return success response
     */
    private function successResponse(string $message, $data = null, int $status = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $status);
    }

    /**
     * Return error response
     */
    private function errorResponse(string $message, array $errors = [], int $status = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $status);
    }
}